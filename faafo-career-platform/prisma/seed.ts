import prisma from '../src/lib/prisma';
import seedAdditionalPaths from './seed-additional-paths';
// import { PrismaClient } from '@prisma/client'; // No longer needed

// const prisma = new PrismaClient(); // No longer needed

async function main() {
  console.log(`Start seeding ...`);

  // 1. Create Career Paths
  const freelanceDevPath = await prisma.careerPath.upsert({
    where: { slug: 'freelance-web-developer' },
    update: {},
    create: {
      name: 'Freelance Web Developer',
      slug: 'freelance-web-developer',
      overview: 'Build websites and web applications for clients on a project basis. High demand driven by digital transformation across industries - from local restaurants needing online ordering to SaaS startups requiring custom dashboards. The rise of no-code tools creates opportunities for specialized development work. No formal degree required, but strong programming skills and portfolio essential. Enjoy flexibility and a variety of work.',
      pros: JSON.stringify([
        'High flexibility in work hours and location',
        'Direct control over projects and clients',
        'Potential for high income based on skill and marketing',
        'Constant exposure to new web technologies and frameworks keeps work exciting',
      ]),
      cons: JSON.stringify([
        'Income can be unstable, especially initially',
        'Need to handle all aspects of business (marketing, contracts, invoicing)',
        'Can be isolating if not proactive in networking',
        'Requires self-discipline and time management',
      ]),
      actionableSteps: [
        'Define your niche and services offered.',
        'Build a strong portfolio using GitHub Pages or Netlify, showcase 5-8 projects with live demos and code repositories',
        'Set up a professional website or online presence.',
        'Join LinkedIn groups, attend local tech meetups via Meetup.com, and engage on X (formerly Twitter) using #webdev hashtags',
        'Complete "Freelance to Freedom" course on Udemy and read "The Freelancer\'s Union Guide to Taxes" (2-4 weeks)',
        'Set up QuickBooks Self-Employed or FreshBooks for invoicing, and use Toggl for time tracking (budget $30-50/month)',
        'Subscribe to CSS-Tricks, follow Wes Bos courses, and complete monthly challenges on Frontend Mentor or Codepen',
      ],
      isActive: true,
    },
  });

  const onlineBusinessPath = await prisma.careerPath.upsert({
    where: { slug: 'simple-online-business' },
    update: {},
    create: {
      name: 'Simple Online Business Owner',
      slug: 'simple-online-business',
      overview: 'Create and manage a small online business, such as an e-commerce store, blog, or niche service. Thriving market driven by $6.2 trillion global e-commerce industry, creator economy worth $104 billion, and growing demand for digital services. Popular niches include dropshipping, affiliate marketing, online courses, and SaaS micro-tools. Work from anywhere with internet access. No formal education required, but business knowledge and digital marketing skills are valuable.',
      pros: JSON.stringify([
        'Low startup costs compared to brick-and-mortar',
        'Global reach and customer base',
        'Scalability potential',
        'Work from anywhere with an internet connection',
      ]),
      cons: JSON.stringify([
        'Must master e-commerce platforms, digital marketing, inventory management, and customer support',
        'Saturated markets like dropshipping and affiliate marketing have intense competition',
        'Time-consuming to build traction and revenue',
        'Reliant on online platforms and tools',
      ]),
      actionableSteps: [
        'Identify a niche market or product idea.',
        'Conduct market research to validate your idea.',
        'Choose an e-commerce platform or website builder.',
        'Use SCORE.org free business plan template and create 3-year financial projections using LivePlan or Excel templates',
        'Create compelling content and product listings.',
        'Implement a digital marketing strategy (SEO, social media).',
        'Focus on providing excellent customer service.',
      ],
      isActive: true,
    },
  });

  // AI/Machine Learning Engineer - High demand, technical focus
  const aiMlEngineerPath = await prisma.careerPath.upsert({
    where: { slug: 'ai-machine-learning-engineer' },
    update: {},
    create: {
      name: 'AI/Machine Learning Engineer',
      slug: 'ai-machine-learning-engineer',
      overview: 'Design, develop, and deploy artificial intelligence and machine learning systems that solve complex business problems and automate decision-making processes. Critical role in $200 billion AI market - from recommendation engines at Netflix and Spotify to autonomous vehicles at Tesla and fraud detection at financial institutions. Typically requires bachelor\'s degree in Computer Science, Engineering, or Mathematics. Strong remote work opportunities available.',
      pros: JSON.stringify([
        'Extremely high demand with 35% projected job growth through 2032',
        'Median salary $155,531 (Glassdoor 2025): Entry $95,000, Senior $180,000+',
        'Cutting-edge technology work with significant impact',
        'Strong remote work opportunities (70% of positions offer remote)',
        'Diverse industry applications from healthcare to finance',
        'Work on cutting-edge AI applications: autonomous vehicles, medical diagnosis, financial fraud detection',
      ]),
      cons: JSON.stringify([
        'Requires strong mathematical and statistical foundation',
        'Steep learning curve with rapidly evolving technologies',
        'Entry-level ML roles often require portfolio projects and internships to stand out from bootcamp graduates',
        'Can involve long debugging sessions with complex algorithms',
        'Pressure to stay current with fast-moving field',
        'May require advanced degree for senior positions',
      ]),
      actionableSteps: [
        'Master Python programming (prerequisite: basic programming knowledge) and key libraries (TensorFlow, PyTorch, scikit-learn)',
        'Complete MIT\'s "Introduction to Linear Algebra" on OCW and Khan Academy\'s Calculus series (12-16 weeks)',
        'Complete online courses: Andrew Ng\'s ML Course, Fast.ai, or Coursera ML Specialization',
        'Create portfolio projects: predictive models, computer vision, NLP applications',
        'Gain experience with cloud platforms (AWS SageMaker, Google AI Platform, Azure ML)',
        'Contribute to open-source ML projects on GitHub',
        'Pursue relevant certifications: Google Cloud ML Engineer, AWS ML Specialty',
        'Network through ML conferences, meetups, and online communities (Kaggle, Reddit r/MachineLearning)',
      ],
      isActive: true,
    },
  });

  // Data Scientist - Analytics and insights focus
  const dataScientistPath = await prisma.careerPath.upsert({
    where: { slug: 'data-scientist' },
    update: {},
    create: {
      name: 'Data Scientist',
      slug: 'data-scientist',
      overview: 'Extract insights from complex datasets to drive business decisions, using statistical analysis, machine learning, and data visualization techniques. Essential for $274 billion big data analytics market - optimizing supply chains at Amazon, personalizing content at TikTok, and predicting patient outcomes in healthcare. Strong remote work culture with flexible arrangements. Typically requires bachelor\'s degree in Statistics, Computer Science, Mathematics, or related STEM field.',
      pros: JSON.stringify([
        'High demand with 36% projected growth (fastest growing occupation)',
        'Median salary $150,768 (Glassdoor 2025): Entry $117,276, Senior $165,000+',
        'Work across diverse industries: tech, healthcare, finance, retail',
        'Strong remote work culture (65% of positions offer remote)',
        'Intellectual stimulation solving complex business problems',
        'High impact on strategic business decisions',
      ]),
      cons: JSON.stringify([
        'Entry-level market saturation in some regions',
        'Must master statistics, linear algebra, Python/R, SQL, and machine learning algorithms',
        'Data quality issues can be frustrating and time-consuming',
        'May spend 60-80% of time on data cleaning rather than analysis',
        'Pressure to deliver actionable insights from messy data',
        'Need to communicate complex findings to non-technical stakeholders',
      ]),
      actionableSteps: [
        'Complete "Python for Data Science" on DataCamp and "SQL for Data Science" on Coursera (8-10 weeks)',
        'Complete Khan Academy Statistics course and Coursera\'s "Introduction to Statistical Learning" (8-12 weeks)',
        'Gain proficiency in data visualization tools (Tableau, Power BI, matplotlib)',
        'Build portfolio with real datasets: Kaggle competitions, public datasets',
        'Choose specialization and complete domain-specific course: Google Analytics for marketing, Financial Modeling for finance, or Healthcare Data Analytics (4-6 weeks)',
        'Complete AWS Cloud Practitioner certification and Apache Spark course on Databricks Academy (6-8 weeks)',
        'Complete "Data Storytelling" course on Coursera and practice with Toastmasters International for presentation skills',
        'Enroll in Springboard Data Science Bootcamp (12 weeks) or pursue Master\'s in Data Science from Georgia Tech Online (2 years part-time)',
      ],
      isActive: true,
    },
  });

  // Cybersecurity Specialist - Security and protection focus
  const cybersecurityPath = await prisma.careerPath.upsert({
    where: { slug: 'cybersecurity-specialist' },
    update: {},
    create: {
      name: 'Cybersecurity Specialist',
      slug: 'cybersecurity-specialist',
      overview: 'Protect organizations from cyber threats by implementing security measures, monitoring systems, and responding to security incidents. Critical role in $173 billion cybersecurity market driven by ransomware attacks costing $20 billion annually, data breaches affecting billions of records annually, and increasing regulatory compliance requirements. Remote work opportunities expanding rapidly. Entry-level positions typically require bachelor\'s degree in Computer Science, Information Technology, or cybersecurity certifications.',
      pros: JSON.stringify([
        'Critical demand with 32% projected job growth through 2032',
        'Median salary $124,910 (BLS 2024): Entry $75,000, Senior $150,000+',
        'High job security due to increasing cyber threats',
        'Remote work opportunities expanding post-pandemic',
        'Diverse career paths: penetration testing, incident response, compliance',
        'Intellectual challenge protecting critical infrastructure and sensitive data',
      ]),
      cons: JSON.stringify([
        'High-stress environment with 24/7 threat monitoring',
        'Must constantly learn new attack vectors, security tools, and threat intelligence',
        'May require on-call availability for security incidents',
        'Steep learning curve with complex technical concepts',
        'Certification maintenance requires ongoing education',
        'Can be isolating work focused on threat detection',
      ]),
      actionableSteps: [
        'Start with foundational certifications: CompTIA Security+, Network+',
        'Complete CompTIA Network+ (prerequisite: basic IT knowledge) certification and A Cloud Guru\'s "Linux Essentials" course (6-8 weeks)',
        'Practice with hands-on labs: TryHackMe, HackTheBox, Cybrary',
        'Specialize in area of interest: ethical hacking, incident response, compliance',
        'Pursue advanced certifications: CISSP, CEH, CISM based on career path',
        'Set up VirtualBox with Kali Linux, Metasploitable, and Windows VMs for penetration testing practice (budget $0-200 for hardware)',
        'Join cybersecurity communities and attend conferences (BSides, DEF CON)',
        'Apply for SOC Analyst positions at companies like IBM, Accenture, or local MSPs (target 10-15 applications/week)',
      ],
      isActive: true,
    },
  });

  console.log(`Created career path: ${freelanceDevPath.name} (ID: ${freelanceDevPath.id})`);
  console.log(`Created career path: ${onlineBusinessPath.name} (ID: ${onlineBusinessPath.id})`);
  console.log(`Created career path: ${aiMlEngineerPath.name} (ID: ${aiMlEngineerPath.id})`);
  console.log(`Created career path: ${dataScientistPath.name} (ID: ${dataScientistPath.id})`);
  console.log(`Created career path: ${cybersecurityPath.name} (ID: ${cybersecurityPath.id})`);

  // 2. Create Sophisticated Suggestion Rules

  // AI/ML Engineer Rules
  await prisma.suggestionRule.create({
    data: {
      careerPathId: aiMlEngineerPath.id,
      questionKey: 'top_skills',
      answerValue: 'technical_programming',
      weight: 8.0,
      notes: 'Strong programming skills are essential for AI/ML engineering.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: aiMlEngineerPath.id,
      questionKey: 'top_skills',
      answerValue: 'data_analysis',
      weight: 7.0,
      notes: 'Data analysis skills directly translate to ML model development.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: aiMlEngineerPath.id,
      questionKey: 'skill_development_interest',
      answerValue: 'artificial_intelligence',
      weight: 9.0,
      notes: 'Direct interest in AI indicates strong career fit.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: aiMlEngineerPath.id,
      questionKey: 'core_values',
      answerValue: 'continuous_learning',
      weight: 6.0,
      notes: 'AI/ML field requires constant learning due to rapid evolution.',
    },
  });

  // Data Scientist Rules
  await prisma.suggestionRule.create({
    data: {
      careerPathId: dataScientistPath.id,
      questionKey: 'top_skills',
      answerValue: 'data_analysis',
      weight: 9.0,
      notes: 'Data analysis is the core skill for data science.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: dataScientistPath.id,
      questionKey: 'top_skills',
      answerValue: 'problem_solving',
      weight: 7.0,
      notes: 'Problem-solving skills essential for extracting insights from data.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: dataScientistPath.id,
      questionKey: 'skill_development_interest',
      answerValue: 'data_science',
      weight: 9.0,
      notes: 'Direct interest in data science indicates strong career fit.',
    },
  });

  // Cybersecurity Rules
  await prisma.suggestionRule.create({
    data: {
      careerPathId: cybersecurityPath.id,
      questionKey: 'top_skills',
      answerValue: 'technical_programming',
      weight: 6.0,
      notes: 'Technical skills valuable for cybersecurity automation and analysis.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: cybersecurityPath.id,
      questionKey: 'skill_development_interest',
      answerValue: 'cybersecurity',
      weight: 9.0,
      notes: 'Direct interest in cybersecurity indicates strong career fit.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: cybersecurityPath.id,
      questionKey: 'core_values',
      answerValue: 'helping_others',
      weight: 5.0,
      notes: 'Cybersecurity professionals protect organizations and individuals.',
    },
  });

  // UX/UI Designer - Creative and user-focused
  const uxUiDesignerPath = await prisma.careerPath.upsert({
    where: { slug: 'ux-ui-designer' },
    update: {},
    create: {
      name: 'UX/UI Designer',
      slug: 'ux-ui-designer',
      overview: 'Create intuitive and engaging user experiences for digital products by researching user needs, designing interfaces, and testing usability. Critical role in $1.7 trillion mobile app economy, fintech innovation, healthcare digitization, and enterprise SaaS platforms. Companies like Airbnb, Spotify, and Stripe compete on user experience quality. High remote work availability with flexible schedules.',
      pros: JSON.stringify([
        'Strong demand with 13% projected growth (faster than average)',
        'Median salary $85,277 (Glassdoor 2025): Entry $60,000, Senior $130,000+',
        'High remote work availability (80% of positions offer remote)',
        'Creative and user-focused work with tangible impact',
        'Work across industries: fintech apps, healthcare platforms, e-commerce sites, SaaS products',
        'Portfolio-based hiring allows career changers to demonstrate skills',
      ]),
      cons: JSON.stringify([
        'Highly competitive field with many career changers',
        'Subjective feedback and design criticism can be challenging',
        'Tight deadlines and changing requirements common',
        'Need to balance user needs with business constraints',
        'Must adapt to frequent updates in Figma, Adobe XD, prototyping tools, and design system trends',
        'May face pressure to compromise design vision for business goals',
      ]),
      actionableSteps: [
        'Complete "Design Rules" course on Skillshare and read "The Elements of Typographic Style" by Robert Bringhurst (4-6 weeks)',
        'Master industry-standard tools: Figma, Sketch, Adobe Creative Suite',
        'Complete "User Research" course on Coursera by University of Michigan and practice with UserTesting.com (3-4 weeks)',
        'Create portfolio website using Webflow or Framer, showcase 3-5 case studies with problem, process, and solution documentation',
        'Complete UX/UI bootcamp or online courses (Google UX Certificate, Coursera)',
        'Complete daily UI challenges on DailyUI.co and redesign 2-3 popular apps (Instagram, Spotify, Airbnb) for portfolio',
        'Network through design communities: Dribbble, Behance, local meetups',
        'Create profiles on Upwork and 99designs, target 3-5 small projects ($500-$1500 each) to build portfolio',
      ],
      isActive: true,
    },
  });

  // Digital Marketing Specialist - Marketing and growth focus
  const digitalMarketingPath = await prisma.careerPath.upsert({
    where: { slug: 'digital-marketing-specialist' },
    update: {},
    create: {
      name: 'Digital Marketing Specialist',
      slug: 'digital-marketing-specialist',
      overview: 'Drive business growth through online marketing strategies including social media, content marketing, SEO, and paid advertising campaigns. Essential role in $455 billion digital advertising market, with businesses spending 54% of marketing budgets online. Growth driven by TikTok commerce, AI-powered personalization, and privacy-first marketing post-iOS 14.5. Excellent remote work opportunities with flexible arrangements. Entry-level positions typically require bachelor\'s degree in Marketing, Communications, or Business, though certifications can substitute.',
      pros: JSON.stringify([
        'Growing field with 10% projected growth through 2032',
        'Entry-level salary range: $45,000-$95,000 (Glassdoor 2025)',
        'High remote work opportunities (75% of positions)',
        'Diverse specializations: SEO, social media, content, PPC',
        'Measurable results and direct impact on business growth',
        'Creative and analytical work combining art and science',
      ]),
      cons: JSON.stringify([
        'Rapidly changing platforms and algorithms require constant adaptation',
        'Performance pressure with ROI and conversion metrics',
        'Can be overwhelming managing multiple channels and campaigns',
        'Budget constraints may limit campaign effectiveness',
        'Saturated channels like Facebook Ads and Google Ads require higher budgets and advanced targeting skills',
        'Work-life balance challenges with always-on digital presence',
      ]),
      actionableSteps: [
        'Complete Google Digital Marketing & E-commerce Certificate on Coursera (3-6 months, 10 hours/week)',
        'Specialize in 1-2 areas: SEO, social media, content marketing, or PPC',
        'Master analytics tools: Google Analytics, Facebook Ads Manager, SEMrush',
        'Create LinkedIn content calendar, start marketing blog on Medium, and document campaigns on personal website with metrics',
        'Get certified in major platforms: Google Ads, Facebook Blueprint, HubSpot',
        'Practice with real campaigns: volunteer for nonprofits or small businesses',
        'Stay current with industry blogs: Moz, Social Media Examiner, Content Marketing Institute',
        'Join Marketing Land Slack, attend Content Marketing World or MozCon (virtual $299-599), and participate in #MarketingTwitter',
      ],
      isActive: true,
    },
  });

  console.log(`Created career path: ${uxUiDesignerPath.name} (ID: ${uxUiDesignerPath.id})`);
  console.log(`Created career path: ${digitalMarketingPath.name} (ID: ${digitalMarketingPath.id})`);

  // UX/UI Designer Rules
  await prisma.suggestionRule.create({
    data: {
      careerPathId: uxUiDesignerPath.id,
      questionKey: 'top_skills',
      answerValue: 'design_creative',
      weight: 9.0,
      notes: 'Creative design skills are fundamental for UX/UI design.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: uxUiDesignerPath.id,
      questionKey: 'skill_development_interest',
      answerValue: 'ux_ui_design',
      weight: 9.0,
      notes: 'Direct interest in UX/UI design indicates strong career fit.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: uxUiDesignerPath.id,
      questionKey: 'core_values',
      answerValue: 'creativity',
      weight: 7.0,
      notes: 'Creativity is essential for innovative design solutions.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: uxUiDesignerPath.id,
      questionKey: 'core_values',
      answerValue: 'helping_others',
      weight: 6.0,
      notes: 'UX designers focus on improving user experiences and solving user problems.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: uxUiDesignerPath.id,
      questionKey: 'desired_outcomes_work_life',
      answerValue: 'very_important',
      weight: 6.0,
      notes: 'UX/UI design offers good work-life balance with remote opportunities.',
    },
  });

  // Digital Marketing Rules
  await prisma.suggestionRule.create({
    data: {
      careerPathId: digitalMarketingPath.id,
      questionKey: 'top_skills',
      answerValue: 'marketing_sales',
      weight: 9.0,
      notes: 'Marketing and sales skills directly apply to digital marketing.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: digitalMarketingPath.id,
      questionKey: 'skill_development_interest',
      answerValue: 'digital_marketing',
      weight: 9.0,
      notes: 'Direct interest in digital marketing indicates strong career fit.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: digitalMarketingPath.id,
      questionKey: 'top_skills',
      answerValue: 'writing_content',
      weight: 7.0,
      notes: 'Content creation skills are valuable for digital marketing campaigns.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: digitalMarketingPath.id,
      questionKey: 'core_values',
      answerValue: 'creativity',
      weight: 6.0,
      notes: 'Digital marketing requires creative campaign development.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: digitalMarketingPath.id,
      questionKey: 'desired_outcomes_work_life',
      answerValue: 'very_important',
      weight: 6.0,
      notes: 'Digital marketing offers flexible remote work opportunities.',
    },
  });

  // Original career path rules (existing)
  await prisma.suggestionRule.create({
    data: {
      careerPathId: freelanceDevPath.id,
      questionKey: 'desired_outcomes_work_life',
      answerValue: 'critical',
      weight: 3.0,
      notes: 'Critical need for work-life balance strongly suggests freelancing.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: freelanceDevPath.id,
      questionKey: 'financial_comfort',
      answerValue: 5,
      weight: 1.5,
      notes: 'High financial comfort makes freelancing transition easier.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: onlineBusinessPath.id,
      questionKey: 'dissatisfaction_triggers',
      answerValue: 'lack_of_growth',
      weight: 2.0,
      notes: 'Lack of growth opportunities can be addressed by starting an online business.',
    },
  });

  console.log(`Main seeding finished. Starting additional paths...`);

  // Seed additional career paths
  await seedAdditionalPaths();

  console.log(`All seeding completed successfully!`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });